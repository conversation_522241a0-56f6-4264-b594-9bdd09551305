'use client';

import { useState, useEffect } from 'react';
import { User, Role, CreateUserDto, UpdateUserDto } from '../../services/userService';
import { userService } from '../../services/userService';
import TextInput from '../forms/TextInput';
import Select from '../forms/Select';
import RolesDropdown from '../roles/RolesDropdown';
import DepartmentDropdown from '../departments/DepartmentDropdown';
import { Department } from '@/types/department';
import { Organization } from '@/types/organization';
import OrganizationDropdown from '../organization/OrganizationDropdown';

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  user?: User | null;
  roles: Role[];
  departments: Department[];
  organizations: Organization[];
}

export default function UserModal({ 
  isOpen, onClose, onSave, user, roles = [], departments = [], organizations = [] 
}: UserModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    phone: '',
    department_id: '',
    organization_id: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    role_ids: [] as string[],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isStaff, setIsStaff] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [isStaffChanged, setIsStaffChanged] = useState(false);

  useEffect(() => {
    if (user) {
      const userIsStaff = !!user.department_id;
      setFormData({
        email: user.email,
        password: '', // Don't populate password for editing
        first_name: user.first_name,
        last_name: user.last_name,
        middle_name: user.middle_name || '',
        phone: user.phone,
        department_id: user.department_id || '',
        organization_id: user.organization_id || '',
        status: user.status,
        role_ids: user.roles?.map(role => role.role_id) || [],
      });
      setIsStaff(userIsStaff);
      setShowOptions(true); // Show options immediately for existing users
    } else {
      setFormData({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        middle_name: '',
        phone: '',
        department_id: '',
        organization_id: '',
        status: 'active',
        role_ids: [],
      });
      setIsStaff(false);
      setShowOptions(false); // Hide options initially for new users
    }
    setError(null);
    setIsStaffChanged(false); // Reset the flag when modal opens/closes or user changes
  }, [user, isOpen]);

  // Monitor isStaff changes to show options on first change
  useEffect(() => {
    if (isStaffChanged) {
      setShowOptions(true);
    }
  }, [isStaff, isStaffChanged]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (user) {
        // Update existing user
        const updateData: UpdateUserDto = {
          email: formData.email,
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name || undefined,
          phone: formData.phone,
          status: formData.status,
          role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
        };

        // Only include password if it's provided
        if (formData.password.trim()) {
          updateData.password = formData.password;
        }

        await userService.updateUser(user.user_id, updateData);
      } else {
        // Create new user
        const createData: CreateUserDto = {
          email: formData.email,
          password: formData.password,
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name || undefined,
          phone: formData.phone,
          status: formData.status,
          role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
        };

        await userService.createUser(createData);
      }

      onSave();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error && 'response' in err &&
        typeof err.response === 'object' && err.response !== null &&
        'data' in err.response && typeof err.response.data === 'object' && err.response.data !== null &&
        'message' in err.response.data && typeof err.response.data.message === 'string'
        ? err.response.data.message
        : 'Failed to save user';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleToggle = (roleId: string | number) => {
    const roleIdStr = String(roleId);
    setFormData(prev => ({
      ...prev,
      role_ids: prev.role_ids.includes(roleIdStr)
        ? prev.role_ids.filter(id => id !== roleIdStr)
        : [...prev.role_ids, roleIdStr]
    }));
  };

  const handleDepartmentSelect = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      department_id: id,
    }));
  };

  const handleOrganizationSelect = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      organization_id: id,
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white rounded-lg dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {user ? 'Edit User' : 'Create New User'}
                  </h3>

                  {error && (
                    <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                      {error}
                    </div>
                  )}

                  <div className="grid grid-cols-1 gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="email"
                        name="email"
                        label="Email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter email address"
                      />

                      <TextInput
                        type="password"
                        name="password"
                        label={`Password ${!user ? '*' : ''}`}
                        required={!user}
                        value={formData.password}
                        onChange={handleChange}
                        placeholder={user ? 'Leave blank to keep current password' : 'Enter password'}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="text"
                        name="first_name"
                        label="First Name"
                        required
                        value={formData.first_name}
                        onChange={handleChange}
                        placeholder="Enter first name"
                      />

                      <TextInput
                        type="text"
                        name="last_name"
                        label="Last Name"
                        required
                        value={formData.last_name}
                        onChange={handleChange}
                        placeholder="Enter last name"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <TextInput
                        type="text"
                        name="middle_name"
                        label="Middle Name"
                        value={formData.middle_name}
                        onChange={handleChange}
                        placeholder="Enter middle name (optional)"
                      />

                      <TextInput
                        type="tel"
                        name="phone"
                        label="Phone"
                        required
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="Enter phone number"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        name="status"
                        label="Status"
                        value={formData.status}
                        onChange={handleChange}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                      </Select>

                      <Select
                        name="isStaff"
                        label="MACRA Staff Member?"
                        value={isStaff ? 'true' : 'false'}
                        onChange={(e) => {
                          setIsStaff(e.target.value === 'true');
                          setIsStaffChanged(true);
                        }}
                      >
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                      </Select>

                      {showOptions && (
                        <>
                          {isStaff ? (
                            <DepartmentDropdown
                              selectedDepartmentId={formData.department_id}
                              onSelect={handleDepartmentSelect}
                              departments={departments}
                            />
                          ) : (
                            <OrganizationDropdown
                              selectedOrganizationId={formData.organization_id}
                              onSelect={handleOrganizationSelect}
                              organizations={organizations}
                            />
                          )}

                          <RolesDropdown
                            roles={roles}
                            formData={formData}
                            handleRoleToggle={handleRoleToggle}
                          />
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading}
                className="main-button inline-flex items-center justify-center w-full sm:w-auto sm:ml-3 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (user ? 'Update User' : 'Create User')}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="secondary-main-button inline-flex items-center justify-center mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}


